[package]
name = "ai-agent-audit"
version = "0.1.0"
edition = "2024"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true

[dependencies]
qdrant-client = { version = "1.14", features = ["serde"] }
tempfile = "3.20"
walkdir = "2.4"
serde = "1.0.219"
ignore = "0.4"
regex = "1.10"
serde_json = { version = "1.0", features = ["preserve_order"] }
tokio = { version = "1.46.1", features = ["macros", "rt-multi-thread"] }
tokio-stream = "0.1.17"
rig-core = { version = "0.13.0", features = ["derive"] }
dotenvy = "0.15"
anyhow = "1.0"
git2 = "0.18"
log = "0.4.26"
reqwest = { version = "0.12.19", features = ["json", "rustls-tls"] }
env_logger = "0.11"
tiktoken-rs = "0.6.0"
rusqlite = { version = "0.31", features = ["bundled"] }                  #  🔒 static libsqlite3
serde-sarif = "0.7"                                                      # straight-forward SARIF model
uuid = { version = "1", features = ["v4"] }
chrono = "0.4"                                                           # timestamp in tickets
async-trait = "0.1.79"
thiserror = "2.0.12"
hex = "0.4.3"
once_cell = "1.8"
async-openai = "0.28.1"
rig-qdrant = "0.1.14"
schemars = "0.8.22"
nanoid = "0.4"
