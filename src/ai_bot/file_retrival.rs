use rig::client::{CompletionClient, EmbeddingsClient};
use crate::prepare_code::git_clone::RepoPaths;
use qdrant_client::{
    qdrant::{Condition, FieldCondition, Filter, Match, MatchText, QueryPointsBuilder, Vector as QdrantVector},
    Qdrant,
};
use rig::providers::openai::{Client, TEXT_EMBEDDING_3_SMALL};
use rig::{completion::ToolDefinition, tool::Tool};
use rig_qdrant::QdrantVectorStore;
use serde::{Deserialize, Serialize};
use thiserror::Error;

#[derive(Deserialize)]
pub struct RetrieveArgs {
    file_type: String, // e.g., "source", "test", "script"
    query: String,     // e.g., function name or keyword
}

#[derive(Serialize)]
pub struct RetrieveOut {
    content: String,
}

#[derive(Debug, Error)]
#[error("Retrieval error: {0}")]
pub enum RetrievalError {
    Qdrant(#[from] qdrant_client::QdrantError),
    Other(#[from] anyhow::Error),
}

pub struct FileRetrievalTool {
    qdrant_url: String,
    openai_api_key: String,
    repo: RepoPaths, // For dynamic collection name
}

impl FileRetrievalTool {
    pub fn new(qdrant_url: String, openai_api_key: String, repo: RepoPaths) -> Self {
        Self {
            qdrant_url,
            openai_api_key,
            repo,
        }
    }
}

impl Tool for FileRetrievalTool {
    const NAME: &'static str = "retrieve_file_content";
    type Args = RetrieveArgs;
    type Output = RetrieveOut;
    type Error = RetrievalError;

    async fn definition(&self, _prompt: String) -> ToolDefinition {
        ToolDefinition {
            name: Self::NAME.to_string(),
            description: "Retrieve content from source code, test, or script files based on type and query using Qdrant vector search.".into(),
            parameters: serde_json::json!({
                "type": "object",
                "properties": {
                    "file_type": { "type": "string", "enum": ["source", "test", "script"] },
                    "query": { "type": "string", "description": "Keyword or ID to search" }
                },
                "required": ["file_type", "query"]
            }),
        }
    }

    async fn call(&self, args: Self::Args) -> Result<Self::Output, Self::Error> {
        let qdrant = Qdrant::from_url(&self.qdrant_url).build()?;

        let openai = Client::new(&self.openai_api_key);
        let embedder = openai.embedding_model(TEXT_EMBEDDING_3_SMALL);

        let collection_name = format!("{}-contract_chunks", self.repo.unique_repo_hash());

        // Embed the query for semantic search
        let query_embedding = embedder.embed_query(&args.query).await?;

        // Filter by file_type using Qdrant payload filter
        let field_condition = FieldCondition {
            key: "file_type".to_string(), // Assuming 'file_type' in payload metadata
            r#match: Some(Match {
                match_value: Some(match::MatchValue::Text(args.file_type))
            }),
            ..Default::default()
        };

        let filter = Filter {
            must: vec![Condition {
                condition_one_of: Some(condition::ConditionOneOf::Field(field_condition)),
            }],
            ..Default::default()
        };

        // Perform search using low-level client for more control
        let search_result = qdrant
            .search_points(
                QueryPointsBuilder::new(&collection_name)
                    .query(super::Query {
                        variant: Some(super::query::Variant::Nearest(super::VectorInput {
                            variant: Some(super::vector_input::Variant::Vector(QdrantVector {
                                data: query_embedding,
                                ..Default::default()
                            })),
                        })),
                    })
                    .limit(5)
                    .filter(Some(filter))
                    .with_payload(true)
                    .build(),
            )
            .await?;

        let content = search_result
            .result
            .iter()
            .filter(|point| point.score > 0.5) // Threshold for relevance
            .map(|point| {
                point.payload
                    .get("text")
                    .and_then(|v| v.as_str())
                    .unwrap_or("")
                    .to_string()
            })
            .collect::<Vec<_>>()
            .join("\n\n");

        if content.is_empty() {
            return Err(RetrievalError::Other(anyhow::anyhow!(
                "No relevant content found"
            )));
        }

        Ok(RetrieveOut { content })
    }
}
