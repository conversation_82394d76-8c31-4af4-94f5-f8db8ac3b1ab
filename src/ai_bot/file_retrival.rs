use rig::client::EmbeddingsClient;
use rig::vector_store::VectorStoreIndex;
use crate::build_brain::enbeddings::SourceChunk;
use crate::prepare_code::git_clone::RepoPaths;
use qdrant_client::{qdrant::QueryPointsBuilder, Qdrant};
use rig::providers::openai::{Client, TEXT_EMBEDDING_3_SMALL};
use rig::{completion::ToolDefinition, tool::Tool};
use rig_qdrant::QdrantVectorStore;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use thiserror::Error;
use tokio::sync::Mutex;

#[derive(Deserialize)]
pub struct RetrieveArgs {
    file_type: String, // e.g., "source", "test", "script"
    query: String,     // e.g., function name or keyword
}

#[derive(Serialize)]
pub struct RetrieveOut {
    content: String,
}

#[derive(Debug, Error)]
#[error("Retrieval error: {0}")]
pub enum RetrievalError {
    Qdrant(#[from] qdrant_client::QdrantError),
    VectorStore(#[from] rig::vector_store::VectorStoreError),
    Other(#[from] anyhow::Error),
}

pub struct FileRetrievalTool {
    qdrant_url: String,
    openai_api_key: String,
    repo: RepoPaths, // For dynamic collection name
}

impl FileRetrievalTool {
    pub fn new(qdrant_url: String, openai_api_key: String, repo: RepoPaths) -> Self {
        Self {
            qdrant_url,
            openai_api_key,
            repo,
        }
    }
}

impl Tool for FileRetrievalTool {
    const NAME: &'static str = "retrieve_file_content";
    type Args = RetrieveArgs;
    type Output = RetrieveOut;
    type Error = RetrievalError;

    async fn definition(&self, _prompt: String) -> ToolDefinition {
        ToolDefinition {
            name: Self::NAME.to_string(),
            description: "Retrieve content from source code, test, or script files based on type and query using Qdrant vector search.".into(),
            parameters: serde_json::json!({
                "type": "object",
                "properties": {
                    "file_type": { "type": "string", "enum": ["source", "test", "script"] },
                    "query": { "type": "string", "description": "Keyword or ID to search" }
                },
                "required": ["file_type", "query"]
            }),
        }
    }

    async fn call(&self, args: Self::Args) -> Result<Self::Output, Self::Error> {
        // Create vector store as before
        let qdrant = Qdrant::from_url(&self.qdrant_url).build()?;
        let openai = Client::new(&self.openai_api_key);
        let model = openai.embedding_model(TEXT_EMBEDDING_3_SMALL);

        let collection_name = format!("{}-contract_chunks", self.repo.unique_repo_hash());
        let qp = QueryPointsBuilder::new(&collection_name)
            .with_payload(true)
            .build();

        let vector_store = QdrantVectorStore::new(qdrant, model, qp);

        // Wrap to satisfy Sync constraint - Arc<Mutex<T>> is Send + Sync when T: Send
        let vector_store = Arc::new(Mutex::new(vector_store));

        // Use async lock for access
        let guard = vector_store.lock().await;
        let search_results = guard.top_n::<SourceChunk>(&args.query, 5).await?;

        // Filter results by file_type if needed and extract content
        let content = search_results
            .iter()
            .filter_map(|(score, _doc_id, source_chunk)| {
                // Only include results with good relevance scores
                if *score > 0.5 {
                    Some(source_chunk.text.clone())
                } else {
                    None
                }
            })
            .collect::<Vec<_>>()
            .join("\n\n");

        if content.is_empty() {
            return Err(RetrievalError::Other(anyhow::anyhow!(
                "No relevant content found"
            )));
        }

        Ok(RetrieveOut { content })
    }
}
