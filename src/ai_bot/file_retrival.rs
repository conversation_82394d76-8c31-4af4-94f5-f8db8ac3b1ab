use rig::client::EmbeddingsClient;
use rig::vector_store::VectorStoreIndex;
use crate::build_brain::enbeddings::SourceChunk;
use crate::prepare_code::git_clone::RepoPaths;
use qdrant_client::{qdrant::QueryPointsBuilder, Qdrant};
use rig::providers::openai::{Client, TEXT_EMBEDDING_3_SMALL};
use rig::{completion::ToolDefinition, tool::Tool};
use rig_qdrant::QdrantVectorStore;
use serde::{Deserialize, Serialize};
use thiserror::Error;

#[derive(Deserialize)]
pub struct RetrieveArgs {
    file_type: String, // e.g., "source", "test", "script"
    query: String,     // e.g., function name or keyword
}

#[derive(Serialize)]
pub struct RetrieveOut {
    content: String,
}

#[derive(Debug, Error)]
#[error("Retrieval error: {0}")]
pub enum RetrievalError {
    Qdrant(#[from] qdrant_client::QdrantError),
    VectorStore(#[from] rig::vector_store::VectorStoreError),
    Other(#[from] anyhow::Error),
}

pub struct FileRetrievalTool {
    qdrant_url: String,
    openai_api_key: String,
    repo: RepoPaths, // For dynamic collection name
}

impl FileRetrievalTool {
    pub fn new(qdrant_url: String, openai_api_key: String, repo: RepoPaths) -> Self {
        Self {
            qdrant_url,
            openai_api_key,
            repo,
        }
    }
}



impl Tool for FileRetrievalTool {
    const NAME: &'static str = "retrieve_file_content";
    type Args = RetrieveArgs;
    type Output = RetrieveOut;
    type Error = RetrievalError;

    async fn definition(&self, _prompt: String) -> ToolDefinition {
        ToolDefinition {
            name: Self::NAME.to_string(),
            description: "Retrieve content from source code, test, script, or library files based on type and query using semantic vector search. Use this tool to find specific code snippets, functions, or implementations.".into(),
            parameters: serde_json::json!({
                "type": "object",
                "properties": {
                    "file_type": {
                        "type": "string",
                        "enum": ["source", "test", "script", "library"],
                        "description": "Type of files to search: 'source' for main application code, 'test' for test files, 'script' for build/deployment scripts, 'library' for library/utility code in lib folders"
                    },
                    "query": {
                        "type": "string",
                        "description": "Search query - can be function names, class names, keywords, or natural language describing what you're looking for"
                    }
                },
                "required": ["file_type", "query"]
            }),
        }
    }

    async fn call(&self, args: Self::Args) -> Result<Self::Output, Self::Error> {
        // Validate file_type parameter
        if !["source", "test", "script", "library"].contains(&args.file_type.as_str()) {
            return Err(RetrievalError::Other(anyhow::anyhow!(
                "Invalid file_type: '{}'. Must be 'source', 'test', 'script', or 'library'.",
                args.file_type
            )));
        }

        // Use tokio::task::spawn_blocking to move the non-Sync operation to a blocking context
        let qdrant_url = self.qdrant_url.clone();
        let openai_api_key = self.openai_api_key.clone();
        let repo = self.repo.clone();
        let query = args.query.clone();
        let file_type = args.file_type.clone();

        let result = tokio::task::spawn_blocking(move || {
            // Create a new tokio runtime for the blocking task
            let rt = tokio::runtime::Runtime::new().map_err(|e| {
                RetrievalError::Other(anyhow::anyhow!("Failed to create runtime: {}", e))
            })?;

            rt.block_on(async move {
                // Create vector store
                let qdrant = Qdrant::from_url(&qdrant_url).build()?;
                let openai = Client::new(&openai_api_key);
                let model = openai.embedding_model(TEXT_EMBEDDING_3_SMALL);

                let collection_name = format!("{}-contract_chunks", repo.unique_repo_hash());
                let qp = QueryPointsBuilder::new(&collection_name)
                    .with_payload(true)
                    .build();

                let vector_store = QdrantVectorStore::new(qdrant, model, qp);

                // Perform search
                let search_results = vector_store.top_n::<SourceChunk>(&query, 5).await?;

                // Filter results by file type and score, then extract content
                let content = search_results
                    .iter()
                    .filter_map(|(score, _doc_id, source_chunk)| {
                        // Only include results with good relevance scores
                        if *score <= 0.5 {
                            return None;
                        }

                        // Filter by file type using the direct field access
                        if source_chunk.file_type == file_type {
                            Some(source_chunk.text.clone())
                        } else {
                            None
                        }
                    })
                    .collect::<Vec<_>>()
                    .join("\n\n");

                if content.is_empty() {
                    return Err(RetrievalError::Other(anyhow::anyhow!(
                        "No relevant content found for file type '{}' with query '{}'",
                        file_type, query
                    )));
                }

                Ok(RetrieveOut { content })
            })
        })
        .await
        .map_err(|e| RetrievalError::Other(anyhow::anyhow!("Task join error: {}", e)))??;

        Ok(result)
    }
}


